#!/usr/bin/env python3
"""
Check if we can access the session and what cookies we get
"""

from curl_cffi import requests

def check_session_access():
    """Check if we can access the session URL"""
    
    # Your current cookies (without auth token)
    cookies = {
        'sidebar': 'false',
        'ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog': '%7B%22distinct_id%22%3A%22f01f1444-9732-4d69-bdbd-304cc2e43fd0%22%2C%22%24sesid%22%3A%5B1748101466406%2C%22019702cc-cd88-7feb-9c38-2ccf5d5208a8%22%2C1748098665864%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fbeta.lmarena.ai%2F%22%7D%7D',
    }
    
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
    }
    
    session_url = 'https://alpha.lmarena.ai/c/7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6'
    
    try:
        print(f"🔗 Accessing: {session_url}")
        
        response = requests.get(
            session_url,
            cookies=cookies,
            headers=headers,
            impersonate="chrome110"
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Session accessible!")
            
            # Check if we get any new cookies
            if hasattr(response, 'cookies') and response.cookies:
                print("🍪 New cookies received:")
                for cookie_name, cookie_value in response.cookies.items():
                    print(f"  {cookie_name}: {cookie_value[:50]}...")
                    if 'auth' in cookie_name.lower():
                        print(f"  ⭐ FOUND AUTH COOKIE: {cookie_name}")
            
            # Check Set-Cookie headers
            set_cookie_header = response.headers.get('set-cookie', '')
            if set_cookie_header and 'arena-auth' in set_cookie_header:
                print("🎯 Found arena-auth in Set-Cookie header!")
                print(f"Set-Cookie: {set_cookie_header[:200]}...")
            
            # Check if the page content suggests we need to log in
            if 'login' in response.text.lower() or 'sign in' in response.text.lower():
                print("⚠️  Page suggests authentication is required")
            elif 'evaluation' in response.text.lower() or 'chat' in response.text.lower():
                print("✅ Page appears to show evaluation content")
            
        elif response.status_code == 401:
            print("❌ Authentication required")
        elif response.status_code == 404:
            print("❌ Session not found")
        elif response.status_code == 403:
            print("❌ Access forbidden")
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function"""
    print("🔍 Session Access Checker")
    print("=" * 30)
    
    accessible = check_session_access()
    
    if accessible:
        print("\n💡 Next steps:")
        print("1. The session is accessible")
        print("2. Check your browser's Developer Tools for 'arena-auth-prod-v1' cookie")
        print("3. Copy that cookie value and add it to the script")
    else:
        print("\n💡 Possible issues:")
        print("1. Session may require authentication")
        print("2. Session may have expired")
        print("3. You may need to create a new session")
        print("\n🔧 Try this:")
        print("1. Go to https://alpha.lmarena.ai/")
        print("2. Create a new evaluation session")
        print("3. Copy the new session URL and all cookies")

if __name__ == "__main__":
    main()
