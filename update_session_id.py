#!/usr/bin/env python3
"""
Helper script to update the session ID in test.py
Usage: python update_session_id.py NEW_SESSION_ID
"""

import sys
import re

def update_session_id(new_session_id):
    """Update the session ID in test.py"""
    
    if not re.match(r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', new_session_id):
        print("❌ Invalid session ID format. Should be a UUID like: 12345678-1234-1234-1234-123456789abc")
        return False
    
    try:
        # Read the current test.py file
        with open('test.py', 'r') as f:
            content = f.read()
        
        # Replace the old session ID with the new one
        old_session_id = "8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4"
        
        # Update all occurrences
        updated_content = content.replace(old_session_id, new_session_id)
        
        # Write back to file
        with open('test.py', 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Successfully updated session ID from {old_session_id} to {new_session_id}")
        print("🚀 You can now run: python test.py")
        return True
        
    except Exception as e:
        print(f"❌ Error updating file: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python update_session_id.py NEW_SESSION_ID")
        print("Example: python update_session_id.py 12345678-1234-1234-1234-123456789abc")
        sys.exit(1)
    
    new_session_id = sys.argv[1]
    update_session_id(new_session_id)
