#!/usr/bin/env python3
"""
Scrip<PERSON> to decode and examine the authentication token
"""

import base64
import json

def decode_token(auth_cookie):
    """Decode and display the authentication token details"""
    try:
        # Extract the base64 part after 'base64-'
        if auth_cookie.startswith('base64-'):
            token_data = auth_cookie[7:]  # Remove 'base64-' prefix
            
            # Decode the base64 token
            decoded_bytes = base64.b64decode(token_data + '==')  # Add padding if needed
            token_info = json.loads(decoded_bytes.decode('utf-8'))
            
            print("🔍 Authentication Token Details:")
            print("=" * 50)
            
            # Extract user information
            user_info = token_info.get('user', {})
            print(f"User ID: {user_info.get('id', 'N/A')}")
            print(f"Email: {user_info.get('email', 'N/A')}")
            print(f"Phone: {user_info.get('phone', 'N/A')}")
            print(f"Role: {user_info.get('role', 'N/A')}")
            print(f"Is Anonymous: {user_info.get('is_anonymous', 'N/A')}")
            
            # Extract metadata
            user_metadata = user_info.get('user_metadata', {})
            print(f"Metadata ID: {user_metadata.get('id', 'N/A')}")
            
            # Extract token info
            print(f"\nToken Type: {token_info.get('token_type', 'N/A')}")
            print(f"Expires At: {token_info.get('expires_at', 'N/A')}")
            print(f"Refresh Token: {token_info.get('refresh_token', 'N/A')}")
            
            return token_info
            
    except Exception as e:
        print(f"❌ Error decoding token: {e}")
        return None

if __name__ == "__main__":
    # The authentication token from the cookies
    auth_token = 'base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSXdNVFE1WXpWa015MHlOVFptTFRRM04yUXRPREl3WlMxbE5UQmxNemt5WTJSbE1qTWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRNE1UQXlNall4TENKcFlYUWlPakUzTkRnd09UZzJOakVzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdGbE9XVXlNVEF0T1daaFl5MDBOakZoTFdFME0yTXROekZsWkdaaVpHUmxNV1F6SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRMk1qWTVOVEE0ZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkwTWpBeE5UUmhZUzFpTnpKaUxUUm1ZVGN0T1dVMk5TMWhZek5oWmpkak5qSmlNREVpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS5ETVh6VWlJX2tsVnB6eWs2eFoxTWFHM1lYcVZMZnE3dEJ0LUpHSnhuRGtNIiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDgxMDIyNjEsInJlZnJlc2hfdG9rZW4iOiJobDZkc2tiand6ZG0iLCJ1c2VyIjp7ImlkIjoiMDE0OWM1ZDMtMjU2Zi00NzdkLTgyMGUtZTUwZTM5MmNkZTIzIiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMDNUMTA6NTE6NDguNzk0NDY5WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwYWU5ZTIxMC05ZmFjLTQ2MWEtYTQzYy03MWVkZmJkZGUxZDMifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTAzVDEwOjUxOjQ4Ljc5MjU0OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yNFQxNDo1Nzo0MS42MzQyNjlaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0'
    
    decode_token(auth_token)
