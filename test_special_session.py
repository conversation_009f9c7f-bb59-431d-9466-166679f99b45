#!/usr/bin/env python3
"""
Test script to demonstrate the special session authentication functionality
"""

import sys
import os

# Add the current directory to the path so we can import from test.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test import get_cookies_for_session, SPECIAL_SESSION_ID, LM_ARENA_COOKIES_PRIMARY, LM_ARENA_COOKIES_SPECIAL

def test_session_authentication():
    """Test that different sessions get different authentication tokens"""
    
    print("🧪 Testing Session-Specific Authentication")
    print("=" * 60)
    
    # Test regular sessions
    regular_sessions = [
        "b1f28112-7b47-40a0-b2ae-29bf7293a104",
        "b0fce5ce-f058-4cb2-b262-d9352d3d201d",
        "some-random-session-id"
    ]
    
    print("🔑 Testing PRIMARY authentication for regular sessions:")
    for session_id in regular_sessions:
        cookies = get_cookies_for_session(session_id)
        auth_token = cookies.get('arena-auth-prod-v1', 'NOT_FOUND')[:50]
        print(f"   Session {session_id[:8]}... → Token: {auth_token}...")
    
    print(f"\n🔒 Testing SPECIAL authentication for session {SPECIAL_SESSION_ID}:")
    special_cookies = get_cookies_for_session(SPECIAL_SESSION_ID)
    special_auth_token = special_cookies.get('arena-auth-prod-v1', 'NOT_FOUND')[:50]
    print(f"   Session {SPECIAL_SESSION_ID[:8]}... → Token: {special_auth_token}...")
    
    # Verify they're different
    primary_token = LM_ARENA_COOKIES_PRIMARY.get('arena-auth-prod-v1', '')[:50]
    special_token = LM_ARENA_COOKIES_SPECIAL.get('arena-auth-prod-v1', '')[:50]
    
    print(f"\n📊 Token Comparison:")
    print(f"   Primary token:  {primary_token}...")
    print(f"   Special token:  {special_token}...")
    
    if primary_token != special_token:
        print("✅ SUCCESS: Different tokens are being used for different sessions!")
    else:
        print("⚠️  WARNING: Same token is being used for both session types!")
        if special_token == "PASTE_SECOND_AUTH_TOKEN_HERE":
            print("💡 TIP: Run 'python update_special_token.py' to set the special token")
    
    print("=" * 60)

def show_configuration():
    """Show the current configuration"""
    print("📋 Current Configuration:")
    print("=" * 40)
    print(f"🔒 Special Session ID: {SPECIAL_SESSION_ID}")
    print(f"🔑 Primary Token: {LM_ARENA_COOKIES_PRIMARY.get('arena-auth-prod-v1', 'NOT_SET')[:30]}...")
    print(f"🔒 Special Token: {LM_ARENA_COOKIES_SPECIAL.get('arena-auth-prod-v1', 'NOT_SET')[:30]}...")
    print("=" * 40)

if __name__ == "__main__":
    show_configuration()
    print()
    test_session_authentication()
    
    print("\n🎯 How it works:")
    print("1. When Flask app randomly selects a session ID")
    print("2. If session ID == 92bed804-c450-41b8-ac6a-be2cac4912c6:")
    print("   → Uses SPECIAL authentication token")
    print("3. For all other session IDs:")
    print("   → Uses PRIMARY authentication token from token.txt")
    print("\n🚀 This ensures session 92bed804-c450-41b8-ac6a-be2cac4912c6")
    print("   ONLY uses its dedicated authentication token!")
