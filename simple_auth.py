#!/usr/bin/env python3
"""
Simple script to get fresh authentication by visiting LM Arena
"""

from curl_cffi import requests
import json
import re

def get_fresh_auth():
    """Get fresh authentication by visiting the site"""
    print("🌐 Getting fresh authentication...")
    
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
    }
    
    try:
        # Visit the main page to get fresh cookies
        response = requests.get(
            'https://alpha.lmarena.ai/',
            headers=headers,
            impersonate="chrome110"
        )
        
        print(f"Status: {response.status_code}")
        
        # Extract cookies from response headers
        cookies_dict = {}
        
        # Get cookies from the response object
        if hasattr(response, 'cookies') and response.cookies:
            for cookie_name, cookie_value in response.cookies.items():
                cookies_dict[cookie_name] = cookie_value
                print(f"🍪 {cookie_name}: {cookie_value[:50]}...")
        
        # Also check Set-Cookie headers
        set_cookie_headers = response.headers.get_list('set-cookie') if hasattr(response.headers, 'get_list') else []
        if not set_cookie_headers:
            set_cookie_header = response.headers.get('set-cookie', '')
            if set_cookie_header:
                set_cookie_headers = [set_cookie_header]
        
        for header in set_cookie_headers:
            # Parse each Set-Cookie header
            cookie_parts = header.split(';')[0].split('=', 1)
            if len(cookie_parts) == 2:
                name, value = cookie_parts
                cookies_dict[name.strip()] = value.strip()
                print(f"🍪 {name.strip()}: {value.strip()[:50]}...")
        
        if cookies_dict:
            print(f"\n✅ Got {len(cookies_dict)} cookies!")
            
            # Print the cookies in Python dict format
            print("\n📋 Fresh cookies for your script:")
            print("=" * 50)
            print("cookies = {")
            for name, value in cookies_dict.items():
                print(f"    '{name}': '{value}',")
            print("}")
            print("=" * 50)
            
            return cookies_dict
        else:
            print("❌ No cookies received")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_with_fresh_cookies(cookies):
    """Test the fresh cookies with a simple request"""
    if not cookies:
        return False
        
    print("\n🧪 Testing fresh cookies...")
    
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    
    try:
        response = requests.get(
            'https://alpha.lmarena.ai/',
            cookies=cookies,
            headers=headers,
            impersonate="chrome110"
        )
        
        print(f"Test Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Fresh cookies are working!")
            return True
        else:
            print("❌ Fresh cookies may not be working properly")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main function"""
    print("🔐 Simple Authentication Token Generator")
    print("=" * 45)
    
    # Get fresh cookies
    fresh_cookies = get_fresh_auth()
    
    if fresh_cookies:
        # Test the cookies
        test_with_fresh_cookies(fresh_cookies)
        
        print("\n💡 Instructions:")
        print("1. Copy the cookies dict above")
        print("2. Replace the cookies in your test.py script")
        print("3. Run your test.py script")
        
        return fresh_cookies
    else:
        print("\n❌ Failed to get fresh authentication")
        print("💡 You may need to:")
        print("   1. Check your internet connection")
        print("   2. Try a different approach")
        print("   3. Manually extract cookies from browser")
        
        return None

if __name__ == "__main__":
    main()
