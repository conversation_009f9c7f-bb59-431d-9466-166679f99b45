#!/usr/bin/env python3
"""
Automated script to create a new evaluation session and get fresh tokens
This bypasses the need for existing tokens by creating everything fresh
"""

from curl_cffi import requests
import json
import uuid
import time

def generate_uuid():
    """Generate a random UUID"""
    return str(uuid.uuid4())

def create_fresh_evaluation_session():
    """Create a completely fresh evaluation session"""
    print("🆕 Creating fresh evaluation session...")
    
    # Generate fresh IDs
    session_id = generate_uuid()
    user_message_id = generate_uuid()
    assistant_message_id = generate_uuid()
    model_a_message_id = generate_uuid()
    
    print(f"📝 Generated session ID: {session_id}")
    
    headers = {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://alpha.lmarena.ai',
        'pragma': 'no-cache',
        'referer': f'https://alpha.lmarena.ai/c/{session_id}',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    
    # Create the data payload with fresh IDs
    data = {
        "id": session_id,
        "mode": "direct",
        "modelAId": "ee116d12-64d6-48a8-88e5-b2d06325cdd2",
        "userMessageId": user_message_id,
        "modelAMessageId": model_a_message_id,
        "messages": [
            {
                "id": user_message_id,
                "role": "user",
                "content": "hi",
                "experimental_attachments": [],
                "parentMessageIds": [],
                "participantPosition": "a",
                "modelId": None,
                "evaluationSessionId": session_id,
                "status": "pending",
                "failureReason": None
            },
            {
                "id": assistant_message_id,
                "role": "assistant",
                "content": "Hello! How can I help you today?",
                "experimental_attachments": [],
                "parentMessageIds": [user_message_id],
                "participantPosition": "a",
                "modelId": "ee116d12-64d6-48a8-88e5-b2d06325cdd2",
                "evaluationSessionId": session_id,
                "status": "success",
                "failureReason": None
            },
            {
                "id": generate_uuid(),
                "role": "user",
                "content": "test",
                "experimental_attachments": [],
                "parentMessageIds": [assistant_message_id],
                "participantPosition": "a",
                "modelId": None,
                "evaluationSessionId": session_id,
                "status": "pending",
                "failureReason": None
            },
            {
                "id": model_a_message_id,
                "role": "assistant",
                "content": "",
                "experimental_attachments": [],
                "parentMessageIds": [user_message_id],
                "participantPosition": "a",
                "modelId": "ee116d12-64d6-48a8-88e5-b2d06325cdd2",
                "evaluationSessionId": session_id,
                "status": "pending",
                "failureReason": None
            }
        ],
        "modality": "chat"
    }
    
    # Try different endpoints that might work without authentication
    endpoints = [
        f'https://alpha.lmarena.ai/api/stream/post-to-evaluation/{session_id}',
        f'https://beta.lmarena.ai/api/stream/post-to-evaluation/{session_id}',
        f'https://alpha.lmarena.ai/api/evaluation/{session_id}',
        f'https://beta.lmarena.ai/api/evaluation/{session_id}',
    ]
    
    for endpoint in endpoints:
        try:
            print(f"🔗 Trying: {endpoint}")
            
            response = requests.post(
                endpoint,
                headers=headers,
                json=data,
                impersonate="chrome110"
            )
            
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
            if response.status_code in [200, 201]:
                print("✅ Success! Session created without authentication!")
                return {
                    'session_id': session_id,
                    'endpoint': endpoint,
                    'data': data,
                    'response': response.text
                }
            elif response.status_code == 401:
                print("❌ Authentication required")
            elif response.status_code == 404:
                print("❌ Endpoint not found")
            else:
                print(f"❌ Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error with {endpoint}: {e}")
            continue
    
    return None

def try_anonymous_request():
    """Try making requests without any authentication"""
    print("🔓 Trying anonymous requests...")
    
    # Simple GET request to see what we can access
    headers = {
        'accept': 'application/json',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    
    endpoints = [
        'https://alpha.lmarena.ai/api/models',
        'https://beta.lmarena.ai/api/models',
        'https://alpha.lmarena.ai/api/health',
        'https://beta.lmarena.ai/api/health',
        'https://alpha.lmarena.ai/api/status',
        'https://beta.lmarena.ai/api/status',
    ]
    
    for endpoint in endpoints:
        try:
            print(f"🔗 Checking: {endpoint}")
            
            response = requests.get(
                endpoint,
                headers=headers,
                impersonate="chrome110"
            )
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                print(f"✅ Success! Response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            continue

def create_working_script(session_data):
    """Create a working script with the new session"""
    if not session_data:
        return
        
    script_content = f'''#!/usr/bin/env python3
"""
Auto-generated script with fresh session data
"""

from curl_cffi import requests
import json

# Fresh session data
session_id = "{session_data['session_id']}"
endpoint = "{session_data['endpoint']}"

headers = {{
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://alpha.lmarena.ai',
    'pragma': 'no-cache',
    'referer': f'https://alpha.lmarena.ai/c/{{session_id}}',
    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}}

data = {json.dumps(session_data['data'], indent=4)}

# Make the request
response = requests.post(
    endpoint,
    headers=headers,
    json=data,
    impersonate="chrome110"
)

print(f"Status: {{response.status_code}}")
print(f"Response: {{response.text}}")
'''
    
    with open('auto_generated_test.py', 'w') as f:
        f.write(script_content)
    
    print("✅ Created auto_generated_test.py")

def main():
    """Main function"""
    print("🤖 Automated Session Creator")
    print("=" * 30)
    
    # Try to create a fresh session
    session_data = create_fresh_evaluation_session()
    
    if session_data:
        print("\n🎉 SUCCESS!")
        print(f"Session ID: {session_data['session_id']}")
        print(f"Endpoint: {session_data['endpoint']}")
        
        # Create a working script
        create_working_script(session_data)
        
        return session_data
    
    # If that fails, try anonymous requests to understand the API
    print("\n🔍 Exploring API endpoints...")
    try_anonymous_request()
    
    print("\n❌ Could not create session automatically")
    print("💡 The API likely requires proper authentication")
    print("💡 You may need to manually get fresh cookies from browser")
    
    return None

if __name__ == "__main__":
    main()
