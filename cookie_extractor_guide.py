#!/usr/bin/env python3
"""
Complete guide and automation for extracting fresh cookies from browser
"""

import json
import base64
from datetime import datetime

def decode_and_check_token(auth_cookie):
    """Decode and check if a token is valid"""
    try:
        if auth_cookie.startswith('base64-'):
            token_data = auth_cookie[7:]
            decoded_bytes = base64.b64decode(token_data + '==')
            token_info = json.loads(decoded_bytes.decode('utf-8'))
            
            expires_at = token_info.get('expires_at', 0)
            current_time = int(datetime.now().timestamp())
            
            user_id = token_info.get('user', {}).get('id', 'Unknown')
            
            print(f"🔍 Token Analysis:")
            print(f"   User ID: {user_id}")
            print(f"   Expires: {datetime.fromtimestamp(expires_at)}")
            print(f"   Current: {datetime.fromtimestamp(current_time)}")
            
            if current_time >= expires_at:
                print(f"   Status: ❌ EXPIRED")
                return False, user_id
            else:
                print(f"   Status: ✅ VALID")
                return True, user_id
                
    except Exception as e:
        print(f"❌ Error checking token: {e}")
        return False, None

def update_test_script(new_cookies, session_id=None):
    """Update the test.py script with new cookies"""
    try:
        # Read current test.py
        with open('test.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the cookies section and replace it
        import re
        
        # Pattern to match the cookies dictionary
        cookies_pattern = r'cookies = \{[^}]+\}'
        
        # Create new cookies string
        new_cookies_str = "cookies = {\n"
        for name, value in new_cookies.items():
            new_cookies_str += f"    '{name}': '{value}',\n"
        new_cookies_str += "}"
        
        # Replace the cookies
        updated_content = re.sub(cookies_pattern, new_cookies_str, content, flags=re.DOTALL)
        
        # If session_id is provided, update that too
        if session_id:
            # Update session IDs in the data
            old_session_pattern = r'"evaluationSessionId": "[^"]+"|"id": "[^"]+"'
            updated_content = re.sub(
                r'"evaluationSessionId": "[^"]+"',
                f'"evaluationSessionId": "{session_id}"',
                updated_content
            )
            # Update the main ID
            updated_content = re.sub(
                r'"id": "[^"]+",\s*"mode": "direct"',
                f'"id": "{session_id}",\n    "mode": "direct"',
                updated_content
            )
        
        # Write back
        with open('test.py', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ test.py updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating test.py: {e}")
        return False

def print_manual_instructions():
    """Print detailed manual instructions"""
    print("\n" + "="*60)
    print("📋 MANUAL COOKIE EXTRACTION GUIDE")
    print("="*60)
    print("""
🔧 STEP 1: Open Your Browser
   1. Go to https://alpha.lmarena.ai/
   2. Create a new evaluation session (click "New Chat" or similar)
   3. Note the session ID from URL: /c/SESSION_ID_HERE

🔧 STEP 2: Extract Cookies
   1. Press F12 to open Developer Tools
   2. Go to "Application" tab (Chrome) or "Storage" tab (Firefox)
   3. Click on "Cookies" → "https://alpha.lmarena.ai"
   4. Copy ALL cookie values, especially:
      - arena-auth-prod-v1 (most important!)
      - _ga, _ga_*, cf_clearance, ph_phc_*

🔧 STEP 3: Use This Script
   Run: python cookie_extractor_guide.py
   Then paste your cookies when prompted

🔧 STEP 4: Test
   Run: python test.py
""")
    print("="*60)

def interactive_cookie_input():
    """Interactive cookie input and validation"""
    print("\n🍪 Interactive Cookie Input")
    print("-" * 30)
    
    cookies = {}
    
    # Essential cookies to collect
    essential_cookies = [
        'arena-auth-prod-v1',
        '_ga',
        'cf_clearance',
        'ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog'
    ]
    
    print("Please paste your cookies one by one:")
    print("(Press Enter with empty value to skip optional cookies)")
    
    for cookie_name in essential_cookies:
        while True:
            value = input(f"\n{cookie_name}: ").strip()
            
            if not value and cookie_name == 'arena-auth-prod-v1':
                print("❌ arena-auth-prod-v1 is required!")
                continue
            elif not value:
                print(f"⏭️  Skipping {cookie_name}")
                break
            else:
                cookies[cookie_name] = value
                
                # Validate auth token if provided
                if cookie_name == 'arena-auth-prod-v1':
                    is_valid, user_id = decode_and_check_token(value)
                    if not is_valid:
                        print("⚠️  Warning: This token appears to be expired!")
                        use_anyway = input("Use anyway? (y/n): ").lower()
                        if use_anyway != 'y':
                            continue
                
                print(f"✅ Added {cookie_name}")
                break
    
    # Ask for additional cookies
    print("\n🔄 Add any additional cookies? (name=value format, empty to finish)")
    while True:
        extra = input("Additional cookie: ").strip()
        if not extra:
            break
        
        if '=' in extra:
            name, value = extra.split('=', 1)
            cookies[name.strip()] = value.strip()
            print(f"✅ Added {name.strip()}")
        else:
            print("❌ Invalid format. Use: name=value")
    
    return cookies

def main():
    """Main interactive function"""
    print("🔐 LM Arena Cookie Extractor & Updater")
    print("=" * 45)
    
    print("\nChoose an option:")
    print("1. Show manual extraction guide")
    print("2. Interactive cookie input")
    print("3. Check existing token in test.py")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == '1':
        print_manual_instructions()
        
    elif choice == '2':
        cookies = interactive_cookie_input()
        
        if cookies:
            print(f"\n✅ Collected {len(cookies)} cookies!")
            
            # Ask for session ID
            session_id = input("\nEnter your session ID (from URL /c/SESSION_ID): ").strip()
            
            # Update the test script
            success = update_test_script(cookies, session_id if session_id else None)
            
            if success:
                print("\n🎉 SUCCESS!")
                print("Your test.py script has been updated with fresh cookies!")
                print("Run: python test.py")
            else:
                print("\n❌ Failed to update script")
                print("You may need to manually update test.py")
        
    elif choice == '3':
        try:
            with open('test.py', 'r') as f:
                content = f.read()
            
            # Extract current auth token
            import re
            auth_match = re.search(r"'arena-auth-prod-v1': '([^']+)'", content)
            if auth_match:
                current_token = auth_match.group(1)
                print(f"\n🔍 Current token in test.py:")
                decode_and_check_token(current_token)
            else:
                print("❌ No auth token found in test.py")
                
        except FileNotFoundError:
            print("❌ test.py not found")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
