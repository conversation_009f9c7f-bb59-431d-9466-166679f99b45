from flask import Flask, request, jsonify, render_template, Response, stream_with_context
import json
import base64
import time
import uuid
from datetime import datetime
from curl_cffi import requests # Assuming lmarena.py's imports are needed
import random

app = Flask(__name__)

# --- Configuration (can be moved to a config file or env vars later) ---
# This is the MODEL_ID from lmarena.py. It might be overridden by the request.
DEFAULT_LM_ARENA_MODEL_ID = "gpt-4o-3214"
sessions = ["b1f28112-7b47-40a0-b2ae-29bf7293a104", "b0fce5ce-f058-4cb2-b262-d9352d3d201d"]

# These cookies are from lmarena.py and are likely expired.
# For a real application, these need to be handled dynamically or updated.
LM_ARENA_COOKIES = {
    '_ga': 'GA1.1.617101004.1746269484',
    '_gcl_au': '1.1.933930721.1746269484',
    # ... (omitted for brevity, should include all cookies from lmarena.py)
    'arena-auth-prod-v1':open('token.txt','r').read(),
    'sidebar': 'false',
    'ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog': '%7B%22distinct_id%22%3A%22f01f1444-9732-4d69-bdbd-304cc2e43fd0%22%2C%22%24sesid%22%3A%5B1748101725364%2C%22019702cc-cd88-7feb-9c38-2ccf5d5208a8%22%2C1748098665864%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fbeta.lmarena.ai%2F%22%7D%7D',
}

LM_ARENA_HEADERS = {
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://beta.lmarena.ai', # Will be changed for alpha
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    # Referer will be set dynamically
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Linux"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
}

# --- Helper functions (to be adapted from lmarena.py) ---

def check_token_validity(auth_cookie):
    """Check if the authentication token is valid and not expired"""
    if not auth_cookie:
        print("Auth cookie not provided for validation.")
        return False
    try:
        if auth_cookie.startswith('base64-'):
            token_data = auth_cookie[7:]
            decoded_bytes = base64.b64decode(token_data + '==')
            token_info = json.loads(decoded_bytes.decode('utf-8'))
            expires_at = token_info.get('expires_at', 0)
            current_time = int(time.time())
            if current_time >= expires_at:
                print(f"❌ Token expired at: {datetime.fromtimestamp(expires_at)}")
                return False
            else:
                print(f"✅ Token valid until: {datetime.fromtimestamp(expires_at)}")
                return True
        else:
            print("❌ Invalid auth cookie format.")
            return False
    except Exception as e:
        print(f"❌ Error checking token: {e}")
        return False

def extract_response_content(response_text):
    """Extracts and concatenates text content from lmarena's response."""
    full_text_content = []
    for line in response_text.splitlines():
        if line.startswith("a0"):
            try:
                json_str = line[4:-1]  # Remove a0[" and "]
                data = json.loads(json_str)
                if data.get("type") == "content_delta" and isinstance(data.get("delta"), dict):
                    text_delta = data["delta"].get("text")
                    if text_delta:
                        full_text_content.append(text_delta)
            except json.JSONDecodeError as e:
                print(f"JSONDecodeError in extract_response_content for line: {line}, error: {e}")
            except Exception as e:
                print(f"Error processing line in extract_response_content {line}: {e}")
    return "".join(full_text_content)

# New generator for streaming raw JSON chunks from 'a0' lines
def stream_lmarena_raw_chunks(response_text):
    """Yields the raw JSON string content from lmarena's 'a0' streamed lines."""
    for line in response_text.splitlines():
        if line.startswith("a0"):
            try:
                json_str = line[4:-1] # Remove a0[" and "]
                yield json_str.replace("\\n","\n")
            except Exception as e:
                print(f"Error yielding raw chunk from line {line}: {e}")


def get_lmarena_response(openai_messages, model_id_override=None, custom_cookies=None, stream=False):
    """
    Sends messages to lmarena API and returns the content or a stream generator.
    openai_messages: list of {"role": "user/assistant", "content": "..."}
    model_id_override: specify a model_id, otherwise uses DEFAULT_LM_ARENA_MODEL_ID
    custom_cookies: dictionary of cookies to use, otherwise uses LM_ARENA_COOKIES
    stream: boolean, if True, returns a generator for streaming chunks
    """
    current_model_id = model_id_override if model_id_override else DEFAULT_LM_ARENA_MODEL_ID
    current_cookies = custom_cookies if custom_cookies else LM_ARENA_COOKIES.copy()

    auth_token = current_cookies.get('arena-auth-prod-v1', '')
    if not check_token_validity(auth_token):
        print("⚠️ Warning: Token appears to be expired or invalid. The request may fail.")
        # For critical failures, might be better to return error immediately:
        # return None, "Authentication token expired or invalid."

    session_id = random.choice(sessions)

    processed_messages = []
    parent_id = None
    last_user_msg_id = None
    model_a_message_id = None

    effective_messages = openai_messages[:]
    if not effective_messages or effective_messages[-1].get("role") != "assistant" or effective_messages[-1].get("content") != "":
        effective_messages.append({"role": "assistant", "content": ""})

    for i, msg_spec in enumerate(effective_messages):
        msg_id = str(uuid.uuid4())
        status = "pending"
        if msg_spec["role"] == "assistant" and i < len(effective_messages) - 1:
            status = "success"
        
        message_data = {
            "id": msg_id, "role": msg_spec["role"], "content": msg_spec["content"],
            "experimental_attachments": [], "parentMessageIds": [parent_id] if parent_id else [],
            "participantPosition": "a", "modelId": current_model_id,
            "evaluationSessionId": session_id, "status": status, "failureReason": None
        }
        processed_messages.append(message_data)
        parent_id = msg_id
        if msg_spec["role"] == "user":
            last_user_msg_id = msg_id
        elif msg_spec["role"] == "assistant" and i == len(effective_messages) - 1:
            model_a_message_id = msg_id

    if not model_a_message_id and processed_messages and processed_messages[-1]["role"] == "assistant":
        model_a_message_id = processed_messages[-1]["id"]
    if not model_a_message_id:
        return None, "Could not determine modelAMessageId."

    if not last_user_msg_id:
        for msg_data in reversed(processed_messages[:-1]):
            if msg_data["role"] == "user":
                last_user_msg_id = msg_data["id"]
                break
    if not last_user_msg_id:
        final_assistant_msg = next((m for m in processed_messages if m["id"] == model_a_message_id), None)
        if final_assistant_msg and final_assistant_msg["parentMessageIds"]:
            last_user_msg_id = final_assistant_msg["parentMessageIds"] # Assuming single parent
        else:
            print("Warning: No userMessageId could be determined. This might cause issues if no user messages exist.")
            # If there are truly no user messages, LMArena might error.
            # Fallback: if there's only a system message and an assistant prompt, this logic might need adjustment
            # or rely on LMArena to handle it. For now, we proceed. If it's critical, return error.
            # return None, "No userMessageId could be determined and no fallback parent found."


    payload_data = {
        "id": session_id, "mode": "battle", "userMessageId": last_user_msg_id,
        "modelAMessageId": model_a_message_id, "modelBMessageId": str(uuid.uuid4()),
        "messages": processed_messages, "modality": "chat"
    }
    json_payload = json.dumps(payload_data)

    def attempt_api_call(api_url, headers, cookies_to_use, payload, impersonate_setting):
        try:
            response = requests.post(
                api_url,
                cookies=cookies_to_use,
                headers=headers,
                data=payload,
                impersonate=impersonate_setting,
                timeout=999
            )
            response.text
            print(f"API: {api_url}, Status: {response.status_code}")
            if response.status_code == 200 and "error" not in response.text.lower():
                return response.text, None
            else:
                err_msg = f"API Error ({api_url}): Status {response.status_code}, Response: {response.text[:500]}"
                print(err_msg)
                return None, err_msg
        except Exception as e:
            err_msg = f"API Exception ({api_url}): {e}"
            print(err_msg)
            return None, err_msg

    # Beta API attempt
    headers_beta = LM_ARENA_HEADERS.copy()
    headers_beta['origin'] = 'https://beta.lmarena.ai'
    headers_beta['referer'] = f'https://beta.lmarena.ai/c/{session_id}'
    api_url_beta = f'https://beta.lmarena.ai/api/stream/post-to-evaluation/{session_id}'
    
    response_text, error_beta = attempt_api_call(api_url_beta, headers_beta, current_cookies, json_payload, "chrome110")

    if response_text:
        if stream:
            return stream_lmarena_raw_chunks(response_text), None
        else:
            return extract_response_content(response_text), None

    # Alpha API attempt (fallback)
    print(f"Falling back to Alpha API. Beta error: {error_beta}")
    headers_alpha = LM_ARENA_HEADERS.copy()
    headers_alpha['origin'] = 'https://alpha.lmarena.ai'
    headers_alpha['referer'] = f'https://alpha.lmarena.ai/c/{session_id}'
    api_url_alpha = f'https://alpha.lmarena.ai/api/stream/post-to-evaluation/{session_id}'

    response_text_alpha, error_alpha = attempt_api_call(api_url_alpha, headers_alpha, current_cookies, json_payload, "chrome110")

    if response_text_alpha:
        if stream:
            return stream_lmarena_raw_chunks(response_text_alpha), None
        else:
            return extract_response_content(response_text_alpha), None
            
    final_error = f"Both Beta and Alpha API requests failed. Beta: {error_beta}, Alpha: {error_alpha}"
    return None, final_error


# --- API Endpoint ---
@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    try:
        LM_ARENA_COOKIES['arena-auth-prod-v1'] = open('token.txt','r').read()
        request_data = request.json
        openai_messages = request_data.get("messages", [])
        model_requested = "claude-sonnet-4-20250514"
        stream_requested = request_data.get("stream", False)

        if not openai_messages:
            return jsonify({"error": "No messages provided"}), 400

        # auth_header = request.headers.get("Authorization")
        # Future: use auth_header to select user-specific cookies or validate access.
        # For now, global LM_ARENA_COOKIES are used by get_lmarena_response.

        if stream_requested:
            chunk_generator, error = get_lmarena_response(
                openai_messages,
                model_id_override=model_requested,
                custom_cookies=LM_ARENA_COOKIES, # Pass appropriate cookies
                stream=True
            )

            if error:
                # OpenAI spec usually returns a JSON error even if stream was requested
                return jsonify({"error": f"LMArena API Error: {error}"}), 500
            if chunk_generator is None:
                 return jsonify({"error": "LMArena API returned no generator and no specific error for streaming."}), 500

            def generate_sse_stream():
                response_id = f"chatcmpl-{uuid.uuid4().hex}"
                created_time = int(time.time())
                
                try:
                    first_chunk = True
                    for raw_json_chunk_str in chunk_generator:
                        try:
                            chunk_data = raw_json_chunk_str
                            delta_payload = {}
                            
                            if raw_json_chunk_str:
                                delta_payload["content"] = raw_json_chunk_str
                                
                                # OpenAI sends role only in the first delta chunk with content.
                                if first_chunk and delta_payload: # only add role if there's content
                                    delta_payload["role"] = "assistant"
                                    first_chunk = False
                            
                            # Only yield if delta_payload is not empty
                            if delta_payload:
                                sse_chunk = {
                                    "id": response_id,
                                    "object": "chat.completion.chunk",
                                    "created": created_time,
                                    "model": model_requested,
                                    "choices": [
                                        {
                                            "index": 0,
                                            "delta": delta_payload,
                                            "finish_reason": None
                                        }
                                    ]
                                }
                                yield f"data: {json.dumps(sse_chunk)}\n\n"

                        except json.JSONDecodeError as e_json:
                            print(f"Error decoding LMArena JSON chunk: {raw_json_chunk_str}, error: {e_json}")
                        except Exception as e_chunk_proc:
                            print(f"Error processing LMArena chunk data: {raw_json_chunk_str}, error: {e_chunk_proc}")
                    
                    # Send the final chunk with finish_reason
                    # (Ensuring created_time is consistent or current time for this final event)
                    final_created_time = int(time.time()) # Can also use 'created_time' from above
                    finish_sse_chunk = {
                        "id": response_id,
                        "object": "chat.completion.chunk",
                        "created": final_created_time,
                        "model": model_requested,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {}, # Empty delta for finish reason
                                "finish_reason": "stop" # LMArena doesn't provide this, assuming 'stop'
                            }
                        ]
                    }
                    yield f"data: {json.dumps(finish_sse_chunk)}\n\n"
                    yield "data: [DONE]\n\n"

                except Exception as e_stream_gen:
                    print(f"Error during SSE stream generation: {e_stream_gen}")
                    # Client will see a prematurely closed stream.
                    # Optionally, try to yield a custom error event if protocol allows and headers not fully sent.
                    # yield f"event: error\ndata: {json.dumps({'message': 'Stream generation error', 'detail': str(e_stream_gen)})}\n\n"
                    # yield "data: [DONE]\n\n" # Still try to send DONE if possible

            return Response(stream_with_context(generate_sse_stream()), mimetype='text/event-stream')

        else: # Non-streaming case
            lm_arena_content, error = get_lmarena_response(
                openai_messages,
                model_id_override=model_requested,
                custom_cookies=LM_ARENA_COOKIES,
                stream=False
            )

            if error:
                return jsonify({"error": f"LMArena API Error: {error}"}), 500
            # lm_arena_content can be an empty string if the model genuinely returns nothing.
            # Check for None explicitly for "no response and no error" type issues.
            if lm_arena_content is None:
                 return jsonify({"error": "LMArena API returned no content and no specific error."}), 500

            response_id = f"chatcmpl-{uuid.uuid4().hex}"
            created_time = int(time.time())
            openai_response = {
                "id": response_id,
                "object": "chat.completion",
                "created": created_time,
                "model": model_requested,
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": lm_arena_content, # Uses corrected extract_response_content
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": { # lmarena doesn't provide token counts
                    "prompt_tokens": None,
                    "completion_tokens": None,
                    "total_tokens": None
                }
            }
            return jsonify(openai_response)

    except Exception as e:
        app.logger.error(f"Error in /v1/chat/completions: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

# --- Playground Route ---
@app.route('/')
def index():
    return render_template('index.html')

if __name__ == '__main__':
    app.run("0.0.0.0", port=32938) # Using port 5001 to avoid conflicts