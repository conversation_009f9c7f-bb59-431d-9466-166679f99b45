#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update the special authentication token for session 92bed804-c450-41b8-ac6a-be2cac4912c6
"""

import re

def update_special_token(new_token):
    """Update the special session token in test.py"""
    try:
        # Read the file
        with open('test.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and replace the special auth token
        pattern = r"'arena-auth-prod-v1': 'PASTE_SECOND_AUTH_TOKEN_HERE'"
        replacement = f"'arena-auth-prod-v1': '{new_token}'"
        
        if pattern not in content:
            # Try to find the existing token pattern
            pattern = r"(\s+'arena-auth-prod-v1': ')([^']+)('.*?# 🔒 ONLY for session 92bed804-c450-41b8-ac6a-be2cac4912c6)"
            replacement = f"\\g<1>{new_token}\\g<3>"
            
        updated_content = re.sub(pattern, replacement, content)
        
        # Write back
        with open('test.py', 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ Successfully updated special session token in test.py!")
        print(f"🔒 Token for session 92bed804-c450-41b8-ac6a-be2cac4912c6: {new_token[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating special token: {e}")
        return False

def main():
    print("🔒 Special Session Token Updater")
    print("=" * 50)
    print("This will update the authentication token ONLY for session:")
    print("92bed804-c450-41b8-ac6a-be2cac4912c6")
    print("=" * 50)
    
    new_token = input("📝 Paste the new authentication token: ").strip()
    
    if not new_token:
        print("❌ No token provided!")
        return
    
    if not new_token.startswith('base64-'):
        print("⚠️  Warning: Token doesn't start with 'base64-', are you sure this is correct?")
        confirm = input("Continue anyway? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ Cancelled")
            return
    
    success = update_special_token(new_token)
    
    if success:
        print("\n🎯 Next steps:")
        print("1. The special token is now configured")
        print("2. When the Flask app randomly selects session 92bed804-c450-41b8-ac6a-be2cac4912c6,")
        print("   it will automatically use the special authentication token")
        print("3. All other sessions will use the primary token from token.txt")
        print("\n🚀 Your Flask app is ready to use session-specific authentication!")

if __name__ == "__main__":
    main()
