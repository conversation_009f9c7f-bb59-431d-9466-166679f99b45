PS C:\Users\<USER>\Downloads\s> python test.py
Status Code: 500
Response Headers: Headers({'date': 'Sat, 24 May 2025 15:14:04 GMT', 'content-type': 'application/json', 'content-length': '166', 'access-control-allow-origin': '*', 'cache-control': 'public, max-age=0, must-revalidate', 'ratelimit-limit': '3000', 'ratelimit-policy': '3000;w=300', 'ratelimit-remaining': '2959', 'ratelimit-reset': '241', 'set-cookie': 'arena-auth-prod-v1=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSTFOek0wWTJWa1pTMHlNVFkzTFRRM09UUXRZVEkyWXkxbVpURmpaRGhrTWpjNU5EUWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRNE1UQXpNalEwTENKcFlYUWlPakUzTkRnd09UazJORFFzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdSbFlqYzJPRGd0WVRGa09DMDBPVFE0TFRreU16UXRNbUk1WW1abVpUQTRZbVE0SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRM09UUXlNamMyZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkyTVdReE5qWTBNaTFsTUdJeExUUTNNMk10WVRjMk9TMW1PRFJtTkRGbU1EZ3hNV0lpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS5vVmtXV0xvR2FLZ1Y3dWY4UTcwRXMwVUtzT2J2RnQyQ1p5dC1JcXoxWGx3IiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDgxMDMyNDQsInJlZnJlc2hfdG9rZW4iOiJ5ajJ5dzZldmx6Y2EiLCJ1c2VyIjp7ImlkIjoiNTczNGNlZGUtMjE2Ny00Nzk0LWEyNmMtZmUxY2Q4ZDI3OTQ0IiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjJUMTk6MzE6MTYuNzM3MDg5WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwZGViNzY4OC1hMWQ4LTQ5NDgtOTIzNC0yYjliZmZlMDhiZDgifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTIyVDE5OjMxOjE2LjczNTQ3OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yNFQxMzo0ODoxOS44MDkyMTRaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0; Max-Age=34560000; Path=/; SameSite=Lax', 'strict-transport-security': 'max-age=63072000; includeSubDomains; preload', 'x-matched-path': '/api/stream/post-to-evaluation/[id]', 'x-request-id': 'c2f1c2c7-2e84-48fe-9515-94d60f1bb620', 'x-vercel-cache': 'MISS', 'x-vercel-id': 'fra1:fra1:sfo1::sfo1::sfo1::4fsnz-1748099644137-4573e327480a', 'x-vercel-request-id': 'c2f1c2c7-2e84-48fe-9515-94d60f1bb620', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '944dbd97bf66c7c8-DUS', 'alt-svc': 'h3=":443"; ma=86400'})
Response Text: {"error":"Failed to save messages in database for evaluation session id: 8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4 and messages ids: beab43e7-9a7e-42ad-9cec-67e545bf4e70"}
Error detected in response, trying with alpha domain instead of beta...
Alpha Status Code: 500
Alpha Response Headers: Headers({'date': 'Sat, 24 May 2025 15:14:07 GMT', 'content-type': 'application/json', 'content-length': '166', 'access-control-allow-origin': '*', 'cache-control': 'public, max-age=0, must-revalidate', 'ratelimit-limit': '3000', 'ratelimit-policy': '3000;w=300', 'ratelimit-remaining': '2972', 'ratelimit-reset': '124', 'set-cookie': 'arena-auth-prod-v1=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSTFOek0wWTJWa1pTMHlNVFkzTFRRM09UUXRZVEkyWXkxbVpURmpaRGhrTWpjNU5EUWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRNE1UQXpNalExTENKcFlYUWlPakUzTkRnd09UazJORFVzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdSbFlqYzJPRGd0WVRGa09DMDBPVFE0TFRreU16UXRNbUk1WW1abVpUQTRZbVE0SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRM09UUXlNamMyZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkyTVdReE5qWTBNaTFsTUdJeExUUTNNMk10WVRjMk9TMW1PRFJtTkRGbU1EZ3hNV0lpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS4tYk0tSzRyaEJMSXdXdlRQV3VVbjd0QVFja05wUjFlcmt2TVpXM3pxTTV3IiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDgxMDMyNDUsInJlZnJlc2hfdG9rZW4iOiJ5ajJ5dzZldmx6Y2EiLCJ1c2VyIjp7ImlkIjoiNTczNGNlZGUtMjE2Ny00Nzk0LWEyNmMtZmUxY2Q4ZDI3OTQ0IiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjJUMTk6MzE6MTYuNzM3MDg5WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwZGViNzY4OC1hMWQ4LTQ5NDgtOTIzNC0yYjliZmZlMDhiZDgifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTIyVDE5OjMxOjE2LjczNTQ3OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yNFQxMzo0ODoxOS44MDkyMTRaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0; Max-Age=34560000; Path=/; SameSite=Lax', 'strict-transport-security': 'max-age=63072000; includeSubDomains; preload', 'x-matched-path': '/api/stream/post-to-evaluation/[id]', 'x-request-id': 'f4f9176c-0d6f-48af-a16d-b0a180838fa3', 'x-vercel-cache': 'MISS', 'x-vercel-id': 'fra1:fra1:sfo1::sfo1::sfo1::6wlzh-1748099644992-04bbac8e480e', 'x-vercel-request-id': 'f4f9176c-0d6f-48af-a16d-b0a180838fa3', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '944dbd9cd8bec7ab-DUS', 'alt-svc': 'h3=":443"; ma=86400'})
Alpha Response Text: {"error":"Failed to save messages in database for evaluation session id: 8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4 and messages ids: beab43e7-9a7e-42ad-9cec-67e545bf4e70"}
PS C:\Users\<USER>\Downloads\s> 500
>> PS C:\Users\<USER>\Downloads\s> python test.py
>> Status Code: 500
>> Response Headers: Headers({'date': 'Sat, 24 May 2025 15:14:04 GMT', 'content-type': 'application/json', 'content-length': '166', 'access-control-allow-origin': '*', 'cache-control': 'public, max-age=0, must-revalidate', 'ratelimit-limit': '3000', 'ratelimit-policy': '3000;w=300', 'ratelimit-remaining': '2959', 'ratelimit-reset': '241', 'set-cookie': 'arena-auth-prod-v1=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSTFOek0wWTJWa1pTMHlNVFkzTFRRM09UUXRZVEkyWXkxbVpURmpaRGhrTWpjNU5EUWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRNE1UQXpNalEwTENKcFlYUWlPakUzTkRnd09UazJORFFzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdSbFlqYzJPRGd0WVRGa09DMDBPVFE0TFRreU16UXRNbUk1WW1abVpUQTRZbVE0SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRM09UUXlNamMyZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkyTVdReE5qWTBNaTFsTUdJeExUUTNNMk10WVRjMk9TMW1PRFJtTkRGbU1EZ3hNV0lpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS5vVmtXV0xvR2FLZ1Y3dWY4UTcwRXMwVUtzT2J2RnQyQ1p5dC1JcXoxWGx3IiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDgxMDMyNDQsInJlZnJlc2hfdG9rZW4iOiJ5ajJ5dzZldmx6Y2EiLCJ1c2VyIjp7ImlkIjoiNTczNGNlZGUtMjE2Ny00Nzk0LWEyNmMtZmUxY2Q4ZDI3OTQ0IiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjJUMTk6MzE6MTYuNzM3MDg5WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwZGViNzY4OC1hMWQ4LTQ5NDgtOTIzNC0yYjliZmZlMDhiZDgifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTIyVDE5OjMxOjE2LjczNTQ3OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yNFQxMzo0ODoxOS44MDkyMTRaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0; Max-Age=34560000; Path=/; SameSite=Lax', 'strict-transport-security': 'max-age=63072000; includeSubDomains; preload', 'x-matched-path': '/api/stream/post-to-evaluation/[id]', 'x-request-id': 'c2f1c2c7-2e84-48fe-9515-94d60f1bb620', 'x-vercel-cache': 'MISS', 'x-vercel-id': 'fra1:fra1:sfo1::sfo1::sfo1::4fsnz-1748099644137-4573e327480a', 'x-vercel-request-id': 'c2f1c2c7-2e84-48fe-9515-94d60f1bb620', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '944dbd97bf66c7c8-DUS', 'alt-svc': 'h3=":443"; ma=86400'})
>> Response Text: {"error":"Failed to save messages in database for evaluation session id: 8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4 and messages ids: beab43e7-9a7e-42ad-9cec-67e545bf4e70"}
>> Error detected in response, trying with alpha domain instead of beta...
>> Alpha Status Code: 500
>> Alpha Response Headers: Headers({'date': 'Sat, 24 May 2025 15:14:07 GMT', 'content-type': 'application/json', 'content-length': '166', 'access-control-allow-origin': '*', 'cache-control': 'public, max-age=0, must-revalidate', 'ratelimit-limit': '3000', 'ratelimit-policy': '3000;w=300', 'ratelimit-remaining': '2972', 'ratelimit-reset': '124', 'set-cookie': 'arena-auth-prod-v1=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSTFOek0wWTJWa1pTMHlNVFkzTFRRM09UUXRZVEkyWXkxbVpURmpaRGhrTWpjNU5EUWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRNE1UQXpNalExTENKcFlYUWlPakUzTkRnd09UazJORFVzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdSbFlqYzJPRGd0WVRGa09DMDBPVFE0TFRreU16UXRNbUk1WW1abVpUQTRZbVE0SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRM09UUXlNamMyZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkyTVdReE5qWTBNaTFsTUdJeExUUTNNMk10WVRjMk9TMW1PRFJtTkRGbU1EZ3hNV0lpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS4tYk0tSzRyaEJMSXdXdlRQV3VVbjd0QVFja05wUjFlcmt2TVpXM3pxTTV3IiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDgxMDMyNDUsInJlZnJlc2hfdG9rZW4iOiJ5ajJ5dzZldmx6Y2EiLCJ1c2VyIjp7ImlkIjoiNTczNGNlZGUtMjE2Ny00Nzk0LWEyNmMtZmUxY2Q4ZDI3OTQ0IiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjJUMTk6MzE6MTYuNzM3MDg5WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwZGViNzY4OC1hMWQ4LTQ5NDgtOTIzNC0yYjliZmZlMDhiZDgifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTIyVDE5OjMxOjE2LjczNTQ3OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yNFQxMzo0ODoxOS44MDkyMTRaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0; Max-Age=34560000; Path=/; SameSite=Lax', 'strict-transport-security': 'max-age=63072000; includeSubDomains; preload', 'x-matched-path': '/api/stream/post-to-evaluation/[id]', 'x-request-id': 'f4f9176c-0d6f-48af-a16d-b0a180838fa3', 'x-vercel-cache': 'MISS', 'x-vercel-id': 'fra1:fra1:sfo1::sfo1::sfo1::6wlzh-1748099644992-04bbac8e480e', 'x-vercel-request-id': 'f4f9176c-0d6f-48af-a16d-b0a180838fa3', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '944dbd9cd8bec7ab-DUS', 'alt-svc': 'h3=":443"; ma=86400'})
>> Alpha Response Text: {"error":"Failed to save messages in database for evaluation session id: 8fa86c69-63bf-42d3-b1b8-6fdb2bceb6f4 and messages ids: beab43e7-9a7e-42ad-9cec-67e545bf4e70"}