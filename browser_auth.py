#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to emulate browser behavior and get fresh authentication tokens
This script visits the LM Arena site and extracts fresh tokens from the response
"""

from curl_cffi import requests
import json
import re
import time

def extract_auth_from_page(html_content):
    """Extract authentication data from the page HTML/JavaScript"""
    try:
        # Look for common patterns where auth tokens might be embedded
        patterns = [
            r'arena-auth-prod-v1["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'access_token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'auth["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'__NEXT_DATA__["\']?\s*[:=]\s*({[^}]+})',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                print(f"🔍 Found potential auth data: {matches[:2]}...")  # Show first 2 matches
                
        return None
        
    except Exception as e:
        print(f"❌ Error extracting auth: {e}")
        return None

def simulate_browser_visit():
    """Simulate a browser visiting LM Arena to get fresh tokens"""
    print("🌐 Simulating browser visit to LM Arena...")
    
    # Use existing cookies but visit the main page to potentially refresh tokens
    cookies = {
        '_ga': 'GA1.1.*********.1747941054',
        '_ga_L5C4D55WJJ': 'GS2.1.s1747941053$o1$g1$t1747942187$j0$l0$h0',
        'cf_clearance': 'uTX4g1C91EAtAXe_oD1.krM4FqVuFauH_RL4C3TzrQ8-1747942273-*******-Dwq_rGO.Yd.csdmEMtD842jeDMZ2Fki3QbOWASM64wbCLCHuOrHDxyC1airydBrKGxZW3sltwrAr4HFzzyKPS_hQ6X8D.mMFA5Ch1mWcIvHulkmLaDZDRuYON43x2ATkF8WRJkEDQ4rjdAMStCLfeRUdp1LnvbRzMzepdUlsSbfY4tX37Z_B9Fir_Jm7aJdSMVqoT5jtlOS_6FLfwaHdSM0Rj9vyofwoYrtBZ9m0PxeWU2e.I8rgzcbWiWB5kHy9_iBkzEh8pAZ.uIf2ZUEcG59YVAYU1QmM5m8NvvTZVdPglO.vJol9NPZHyWQiQ5kEG6svlFc65j7UzvjicJX9KIpSc9O3x3B4ENhmVHyqKgO98Eh9uB.o8.BswUglX0tg',
        'ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog': '%7B%22distinct_id%22%3A%220deb7688-a1d8-4948-9234-2b9bffe08bd8%22%2C%22%24sesid%22%3A%5B1747942388004%2C%220196f967-d47f-7c28-af63-5b0f114edc56%22%2C1747941053567%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Falpha.lmarena.ai%2F%22%7D%7D',
        '_ga_72FK1TMV06': 'GS2.1.s1747942207$o1$g1$t1747942388$j0$l0$h0',
    }
    
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    }
    
    urls_to_try = [
        'https://alpha.lmarena.ai/',
        'https://beta.lmarena.ai/',
        'https://alpha.lmarena.ai/c/7c05b741-0e4a-4d8d-bd5c-5ae0e6b801d6',  # Your session
    ]
    
    for url in urls_to_try:
        try:
            print(f"🔗 Visiting: {url}")
            
            response = requests.get(
                url,
                cookies=cookies,
                headers=headers,
                impersonate="chrome110",
                allow_redirects=True
            )
            
            print(f"Status: {response.status_code}")
            
            # Check for new cookies in response
            if response.cookies:
                print("🍪 New cookies received:")
                for cookie in response.cookies:
                    print(f"  {cookie.name}: {cookie.value[:50]}...")
                    if cookie.name == 'arena-auth-prod-v1':
                        print(f"✅ Found new auth token!")
                        return cookie.value
            
            # Check response headers for auth info
            if 'set-cookie' in response.headers:
                set_cookie_header = response.headers['set-cookie']
                if 'arena-auth-prod-v1' in set_cookie_header:
                    print("✅ Found auth token in headers!")
                    # Extract the token value
                    match = re.search(r'arena-auth-prod-v1=([^;]+)', set_cookie_header)
                    if match:
                        return match.group(1)
            
            # Look for auth data in page content
            if response.text:
                auth_data = extract_auth_from_page(response.text)
                if auth_data:
                    return auth_data
                    
        except Exception as e:
            print(f"❌ Error visiting {url}: {e}")
            continue
    
    return None

def create_fresh_session():
    """Create a completely fresh session by visiting the site without any cookies"""
    print("🆕 Creating fresh session...")
    
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    }
    
    try:
        # Visit without any cookies to get a fresh session
        response = requests.get(
            'https://alpha.lmarena.ai/',
            headers=headers,
            impersonate="chrome110",
            allow_redirects=True
        )
        
        print(f"Status: {response.status_code}")
        
        # Collect all cookies from the response
        fresh_cookies = {}
        if response.cookies:
            print("🍪 Fresh cookies received:")
            for cookie in response.cookies:
                fresh_cookies[cookie.name] = cookie.value
                print(f"  {cookie.name}: {cookie.value[:50]}...")
                
        # Check headers for additional cookies
        if 'set-cookie' in response.headers:
            print("🍪 Additional cookies in headers")
            
        return fresh_cookies
        
    except Exception as e:
        print(f"❌ Error creating fresh session: {e}")
        return None

def main():
    """Main function"""
    print("🔐 Browser Authentication Emulator")
    print("=" * 40)
    
    # Try to get fresh token with existing cookies
    fresh_token = simulate_browser_visit()
    
    if fresh_token:
        print(f"\n✅ SUCCESS! Fresh token obtained:")
        print("=" * 60)
        print(f"'arena-auth-prod-v1': '{fresh_token}'")
        print("=" * 60)
        return fresh_token
    
    # If that fails, try creating a completely fresh session
    print("\n🔄 Trying fresh session approach...")
    fresh_cookies = create_fresh_session()
    
    if fresh_cookies:
        print(f"\n✅ Fresh session created!")
        print("=" * 60)
        print("Fresh cookies:")
        for name, value in fresh_cookies.items():
            print(f"'{name}': '{value}',")
        print("=" * 60)
        
        if 'arena-auth-prod-v1' in fresh_cookies:
            return fresh_cookies['arena-auth-prod-v1']
    
    print("\n❌ Could not obtain fresh authentication token")
    print("💡 Manual browser extraction may be required")
    return None

if __name__ == "__main__":
    main()
