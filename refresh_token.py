#!/usr/bin/env python3
"""
Script to refresh authentication tokens for LM Arena
This script uses the refresh token to get a new access token
"""

from curl_cffi import requests
import json
import base64
import time
from datetime import datetime

def decode_token(auth_cookie):
    """Decode the authentication token to extract refresh token"""
    try:
        if auth_cookie.startswith('base64-'):
            token_data = auth_cookie[7:]
            decoded_bytes = base64.b64decode(token_data + '==')
            token_info = json.loads(decoded_bytes.decode('utf-8'))
            return token_info
    except Exception as e:
        print(f"❌ Error decoding token: {e}")
        return None

def refresh_auth_token(refresh_token, cookies):
    """Use the refresh token to get a new access token"""
    print("🔄 Attempting to refresh authentication token...")
    
    # Supabase refresh endpoint (common pattern for auth services)
    refresh_urls = [
        'https://beta.lmarena.ai/auth/v1/token?grant_type=refresh_token',
        'https://alpha.lmarena.ai/auth/v1/token?grant_type=refresh_token',
        'https://beta.lmarena.ai/api/auth/refresh',
        'https://alpha.lmarena.ai/api/auth/refresh',
    ]
    
    headers = {
        'accept': 'application/json',
        'content-type': 'application/json',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    }
    
    refresh_payload = {
        'refresh_token': refresh_token
    }
    
    for url in refresh_urls:
        try:
            print(f"🔗 Trying: {url}")
            response = requests.post(
                url,
                cookies=cookies,
                headers=headers,
                json=refresh_payload,
                impersonate="chrome110"
            )
            
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
            if response.status_code == 200:
                new_token_data = response.json()
                print("✅ Token refresh successful!")
                return new_token_data
                
        except Exception as e:
            print(f"❌ Error with {url}: {e}")
            continue
    
    return None

def create_anonymous_session():
    """Create a new anonymous session"""
    print("🆕 Creating new anonymous session...")
    
    # Try to create anonymous session
    session_urls = [
        'https://beta.lmarena.ai/auth/v1/signup',
        'https://alpha.lmarena.ai/auth/v1/signup',
    ]
    
    headers = {
        'accept': 'application/json',
        'content-type': 'application/json',
        'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    }
    
    # Anonymous signup payload
    anonymous_payload = {
        'email': '',
        'password': '',
        'data': {},
        'gotrue_meta_security': {}
    }
    
    for url in session_urls:
        try:
            print(f"🔗 Trying: {url}")
            response = requests.post(
                url,
                headers=headers,
                json=anonymous_payload,
                impersonate="chrome110"
            )
            
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
            if response.status_code in [200, 201]:
                session_data = response.json()
                print("✅ Anonymous session created!")
                return session_data
                
        except Exception as e:
            print(f"❌ Error with {url}: {e}")
            continue
    
    return None

def update_cookies_with_new_token(new_token_data):
    """Create new cookies with the refreshed token"""
    try:
        # Encode the new token data as base64
        token_json = json.dumps(new_token_data)
        encoded_token = base64.b64encode(token_json.encode('utf-8')).decode('utf-8')
        new_auth_cookie = f"base64-{encoded_token}"
        
        print("✅ New authentication cookie generated!")
        print(f"New token expires at: {datetime.fromtimestamp(new_token_data.get('expires_at', 0))}")
        
        return new_auth_cookie
        
    except Exception as e:
        print(f"❌ Error creating new cookie: {e}")
        return None

def main():
    """Main function to refresh tokens"""
    print("🔐 LM Arena Token Refresher")
    print("=" * 40)
    
    # Current expired token
    current_auth_cookie = 'base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0ltdHBaQ0k2SWtOVFQwNHhkM05uU0hkRlNFTkNNbGNpTENKMGVYQWlPaUpLVjFRaWZRLmV5SnBjM01pT2lKb2RIUndjem92TDJoMWIyZDZiMlZ4ZW1OeVpIWnJkM1IyYjJScExuTjFjR0ZpWVhObExtTnZMMkYxZEdndmRqRWlMQ0p6ZFdJaU9pSTFOek0wWTJWa1pTMHlNVFkzTFRRM09UUXRZVEkyWXkxbVpURmpaRGhrTWpjNU5EUWlMQ0poZFdRaU9pSmhkWFJvWlc1MGFXTmhkR1ZrSWl3aVpYaHdJam94TnpRM09UUTFPRGMyTENKcFlYUWlPakUzTkRjNU5ESXlOellzSW1WdFlXbHNJam9pSWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT250OUxDSjFjMlZ5WDIxbGRHRmtZWFJoSWpwN0ltbGtJam9pTUdSbFlqYzJPRGd0WVRGa09EMDBPVFE0TFRreU16UXRNbUk1WW1abVpUQTRZbVE0SW4wc0luSnZiR1VpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWVdGc0lqb2lZV0ZzTVNJc0ltRnRjaUk2VzNzaWJXVjBhRzlrSWpvaVlXNXZibmx0YjNWeklpd2lkR2x0WlhOMFlXMXdJam94TnpRM09UUXlNamMyZlYwc0luTmxjM05wYjI1ZmFXUWlPaUkyTVdReE5qWTBNaTFsTUdJeExUUTNNMk10WVRjMk9TMW1PRFJtTkRGbU1EZ3hNV0lpTENKcGMxOWhibTl1ZVcxdmRYTWlPblJ5ZFdWOS45NXhSamx4a2xWTWdmYWVhaVNYOGdHZV9JZEhLcTdpLUJMZmVrSjk2Q09VIiwidG9rZW5fdHlwZSI6ImJlYXJlciIsImV4cGlyZXNfaW4iOjM2MDAsImV4cGlyZXNfYXQiOjE3NDc5NDU4NzYsInJlZnJlc2hfdG9rZW4iOiJzZXRvZm9wZ3hhdmwiLCJ1c2VyIjp7ImlkIjoiNTczNGNlZGUtMjE2Ny00Nzk0LWEyNmMtZmUxY2Q4ZDI3OTQ0IiwiYXVkIjoiYXV0aGVudGljYXRlZCIsInJvbGUiOiJhdXRoZW50aWNhdGVkIiwiZW1haWwiOiIiLCJwaG9uZSI6IiIsImxhc3Rfc2lnbl9pbl9hdCI6IjIwMjUtMDUtMjJUMTk6MzE6MTYuNzM3MDg5Nzc0WiIsImFwcF9tZXRhZGF0YSI6e30sInVzZXJfbWV0YWRhdGEiOnsiaWQiOiIwZGViNzY4OC1hMWQ4LTQ5NDgtOTIzNC0yYjliZmZlMDhiZDgifSwiaWRlbnRpdGllcyI6W10sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTIyVDE5OjMxOjE2LjczNTQ3OFoiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNS0yMlQxOTozMToxNi43Mzg1MDlaIiwiaXNfYW5vbnltb3VzIjp0cnVlfX0'
    
    # Current cookies
    cookies = {
        '_ga': 'GA1.1.*********.1747941054',
        '_ga_L5C4D55WJJ': 'GS2.1.s1747941053$o1$g1$t1747942187$j0$l0$h0',
        'cf_clearance': 'uTX4g1C91EAtAXe_oD1.krM4FqVuFauH_RL4C3TzrQ8-1747942273-*******-Dwq_rGO.Yd.csdmEMtD842jeDMZ2Fki3QbOWASM64wbCLCHuOrHDxyC1airydBrKGxZW3sltwrAr4HFzzyKPS_hQ6X8D.mMFA5Ch1mWcIvHulkmLaDZDRuYON43x2ATkF8WRJkEDQ4rjdAMStCLfeRUdp1LnvbRzMzepdUlsSbfY4tX37Z_B9Fir_Jm7aJdSMVqoT5jtlOS_6FLfwaHdSM0Rj9vyofwoYrtBZ9m0PxeWU2e.I8rgzcbWiWB5kHy9_iBkzEh8pAZ.uIf2ZUEcG59YVAYU1QmM5m8NvvTZVdPglO.vJol9NPZHyWQiQ5kEG6svlFc65j7UzvjicJX9KIpSc9O3x3B4ENhmVHyqKgO98Eh9uB.o8.BswUglX0tg',
        'arena-auth-prod-v1': current_auth_cookie,
        'ph_phc_LG7IJbVJqBsk584rbcKca0D5lV2vHguiijDrVji7yDM_posthog': '%7B%22distinct_id%22%3A%220deb7688-a1d8-4948-9234-2b9bffe08bd8%22%2C%22%24sesid%22%3A%5B1747942388004%2C%220196f967-d47f-7c28-af63-5b0f114edc56%22%2C1747941053567%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Falpha.lmarena.ai%2F%22%7D%7D',
        '_ga_72FK1TMV06': 'GS2.1.s1747942207$o1$g1$t1747942388$j0$l0$h0',
    }
    
    # Decode current token to get refresh token
    token_info = decode_token(current_auth_cookie)
    if not token_info:
        print("❌ Could not decode current token")
        return
    
    refresh_token = token_info.get('refresh_token')
    if not refresh_token:
        print("❌ No refresh token found")
        return
    
    print(f"🔍 Found refresh token: {refresh_token}")
    
    # Try to refresh the token
    new_token_data = refresh_auth_token(refresh_token, cookies)
    
    if new_token_data:
        # Create new auth cookie
        new_auth_cookie = update_cookies_with_new_token(new_token_data)
        if new_auth_cookie:
            print("\n🎉 SUCCESS! Here's your new authentication cookie:")
            print("=" * 60)
            print(f"'arena-auth-prod-v1': '{new_auth_cookie}'")
            print("=" * 60)
            print("\n💡 Copy this value and update your test.py script!")
            return new_auth_cookie
    
    # If refresh failed, try creating new anonymous session
    print("\n🔄 Refresh failed, trying to create new anonymous session...")
    new_session_data = create_anonymous_session()
    
    if new_session_data:
        new_auth_cookie = update_cookies_with_new_token(new_session_data)
        if new_auth_cookie:
            print("\n🎉 SUCCESS! Here's your new authentication cookie:")
            print("=" * 60)
            print(f"'arena-auth-prod-v1': '{new_auth_cookie}'")
            print("=" * 60)
            print("\n💡 Copy this value and update your test.py script!")
            print("⚠️  Note: This is a new session, so you may need to create a new evaluation session too.")
            return new_auth_cookie
    
    print("❌ All token refresh attempts failed")
    print("💡 You may need to manually get fresh cookies from your browser")
    return None

if __name__ == "__main__":
    main()
